version: 1
applications:
  - appRoot: frontend
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline --ignore-scripts
            - echo "Pre-build completed"
        build:
          commands:
            - echo "Building with environment variables:"
            - echo "NODE_ENV=$NODE_ENV"
            - echo "NEXTAUTH_URL=$NEXTAUTH_URL"
            - echo "COGNITO_CLIENT_ID=$COGNITO_CLIENT_ID"
            - echo "NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL"
            - npm run build
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
          - .next/cache/**/*
          - .npm/**/*
    envVars:
      # AWS API Configuration
      NEXT_PUBLIC_API_BASE_URL: https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev
      NEXT_PUBLIC_AWS_REGION: ap-northeast-1
      NEXT_PUBLIC_USE_AWS_API: 'true'
      
      # Authentication - NextAuth.js with AWS Cognito
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXTAUTH_URL: ${NEXTAUTH_URL}
      
      # AWS Cognito Configuration
      COGNITO_CLIENT_ID: 2jh76f894g6lv9vrus4qbb9hu7
      COGNITO_ISSUER: https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_H2kKHGUAT
      
      # Feature Flags
      NEXT_PUBLIC_ENABLE_ANALYTICS: 'false'
      NEXT_PUBLIC_ENABLE_DEBUG: 'false'
      NEXT_PUBLIC_ENABLE_MOCK_FALLBACK: 'false'
      NEXT_PUBLIC_ENABLE_MOCK_LOGIN: 'false'
      
      # Environment
      NODE_ENV: production